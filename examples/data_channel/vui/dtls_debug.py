#!/usr/bin/env python3
"""
DTLS Debug Script for Go2 WebRTC Data Channel Issues

This script provides detailed debugging information for DTLS transport issues
that occur on macOS/Ubuntu but not on Windows.
"""

import asyncio
import logging
import ssl
import platform
import sys
import os
from go2_webrtc_driver.webrtc_driver import Go2WebRTCConnection, WebRTCConnectionMethod

# Enable detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Enable aiortc debug logging
logging.getLogger('aiortc').setLevel(logging.DEBUG)
logging.getLogger('aioice').setLevel(logging.DEBUG)

def print_system_info():
    """Print detailed system information for debugging"""
    print("=" * 60)
    print("SYSTEM INFORMATION")
    print("=" * 60)
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.machine()}")
    print(f"Python version: {platform.python_version()}")
    print(f"Python SSL version: {ssl.OPENSSL_VERSION}")
    print(f"SSL version info: {ssl.OPENSSL_VERSION_INFO}")
    
    # Check for OpenSSL library location
    try:
        import OpenSSL
        print(f"PyOpenSSL version: {OpenSSL.__version__}")
    except ImportError:
        print("PyOpenSSL not available")
    
    # Check environment variables that might affect SSL/TLS
    ssl_env_vars = ['SSL_CERT_FILE', 'SSL_CERT_DIR', 'REQUESTS_CA_BUNDLE', 'CURL_CA_BUNDLE']
    for var in ssl_env_vars:
        value = os.environ.get(var)
        if value:
            print(f"{var}: {value}")
    
    print("=" * 60)

def print_ssl_context_info():
    """Print SSL context information"""
    print("SSL CONTEXT INFORMATION")
    print("=" * 60)
    
    # Default SSL context
    ctx = ssl.create_default_context()
    print(f"Default SSL context protocol: {ctx.protocol}")
    print(f"Default SSL context options: {ctx.options}")
    print(f"Default SSL context verify mode: {ctx.verify_mode}")
    print(f"Default SSL context check hostname: {ctx.check_hostname}")
    
    # Available SSL/TLS versions
    print(f"Available SSL/TLS protocols:")
    for attr in dir(ssl):
        if attr.startswith('PROTOCOL_'):
            try:
                value = getattr(ssl, attr)
                print(f"  {attr}: {value}")
            except:
                pass
    
    print("=" * 60)

async def test_dtls_connection():
    """Test DTLS connection with detailed debugging"""
    print("TESTING DTLS CONNECTION")
    print("=" * 60)
    
    try:
        # Create connection
        conn = Go2WebRTCConnection(WebRTCConnectionMethod.LocalSTA, ip="***********")
        
        # Hook into the peer connection to monitor DTLS more closely
        original_connect = conn.connect
        
        async def debug_connect():
            await original_connect()
            
            # Try to access DTLS transport directly
            pc = conn.pc
            if hasattr(pc, '_RTCPeerConnection__sctp') and pc._RTCPeerConnection__sctp:
                sctp = pc._RTCPeerConnection__sctp
                print(f"SCTP transport found: {sctp}")
                
                if hasattr(sctp, 'transport') and sctp.transport:
                    dtls = sctp.transport
                    print(f"DTLS transport found: {dtls}")
                    print(f"DTLS transport state: {dtls.state}")
                    
                    # Try to get more DTLS details
                    if hasattr(dtls, '_RTCDtlsTransport__transport'):
                        underlying = dtls._RTCDtlsTransport__transport
                        print(f"Underlying transport: {underlying}")
                    
                    if hasattr(dtls, '_RTCDtlsTransport__role'):
                        role = dtls._RTCDtlsTransport__role
                        print(f"DTLS role: {role}")
                    
                    # Monitor state changes
                    @dtls.on("statechange")
                    def on_dtls_state():
                        print(f"DTLS state changed to: {dtls.state}")
                        if dtls.state == "failed":
                            print("DTLS FAILED!")
                            # Try to get error information
                            try:
                                if hasattr(dtls, '_RTCDtlsTransport__ssl_context'):
                                    ctx = dtls._RTCDtlsTransport__ssl_context
                                    print(f"SSL context: {ctx}")
                            except Exception as e:
                                print(f"Could not get SSL context: {e}")
        
        conn.connect = debug_connect
        
        # Connect with timeout
        await asyncio.wait_for(conn.connect(), timeout=60)
        
        print("Connection successful!")
        
        # Try to use data channel
        if conn.datachannel and conn.datachannel.data_channel_opened:
            print("Data channel is open!")
        else:
            print("Data channel failed to open")
            
    except asyncio.TimeoutError:
        print("Connection timed out")
    except Exception as e:
        print(f"Connection failed: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main debugging function"""
    print_system_info()
    print_ssl_context_info()
    await test_dtls_connection()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
        sys.exit(0)
