#!/usr/bin/env python3
"""
Ubuntu-specific DTLS Debug Script

This script investigates DTLS issues on Ubuntu with OpenSSL 1.1.1f
"""

import asyncio
import logging
import ssl
import platform
import sys
import os
import subprocess
from go2_webrtc_driver.webrtc_driver import Go2WebRTCConnection, WebRTCConnectionMethod

# Enable detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def print_detailed_system_info():
    """Print comprehensive system information for Ubuntu debugging"""
    print("=" * 80)
    print("UBUNTU SYSTEM ANALYSIS")
    print("=" * 80)
    
    # Basic system info
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Distribution: {platform.platform()}")
    print(f"Architecture: {platform.machine()}")
    print(f"Python version: {platform.python_version()}")
    
    # OpenSSL information
    print(f"\nPython SSL version: {ssl.OPENSSL_VERSION}")
    print(f"SSL version info: {ssl.OPENSSL_VERSION_INFO}")
    
    # Check system OpenSSL
    try:
        result = subprocess.run(['openssl', 'version'], capture_output=True, text=True)
        print(f"System OpenSSL: {result.stdout.strip()}")
    except Exception as e:
        print(f"Could not get system OpenSSL version: {e}")
    
    # Check for multiple OpenSSL installations
    try:
        result = subprocess.run(['dpkg', '-l', '|', 'grep', 'ssl'], shell=True, capture_output=True, text=True)
        print(f"\nInstalled SSL packages:")
        print(result.stdout)
    except Exception as e:
        print(f"Could not list SSL packages: {e}")
    
    # Check Python SSL module details
    try:
        import _ssl
        print(f"\nPython _ssl module: {_ssl}")
        if hasattr(_ssl, 'OPENSSL_VERSION'):
            print(f"_ssl.OPENSSL_VERSION: {_ssl.OPENSSL_VERSION}")
    except Exception as e:
        print(f"Could not get _ssl info: {e}")
    
    # Check for PyOpenSSL
    try:
        import OpenSSL
        print(f"\nPyOpenSSL version: {OpenSSL.__version__}")
        print(f"PyOpenSSL SSL version: {OpenSSL.SSL.SSLeay_version(OpenSSL.SSL.SSLEAY_VERSION)}")
    except ImportError:
        print("\nPyOpenSSL not available")
    except Exception as e:
        print(f"PyOpenSSL error: {e}")
    
    # Check environment variables
    ssl_env_vars = ['SSL_CERT_FILE', 'SSL_CERT_DIR', 'OPENSSL_CONF', 'LD_LIBRARY_PATH']
    print(f"\nSSL Environment Variables:")
    for var in ssl_env_vars:
        value = os.environ.get(var, 'Not set')
        print(f"  {var}: {value}")
    
    # Check aiortc version
    try:
        import aiortc
        print(f"\naiortc version: {aiortc.__version__}")
    except Exception as e:
        print(f"aiortc info error: {e}")
    
    # Check for known Ubuntu-specific issues
    print(f"\nUbuntu-specific checks:")
    
    # Check Ubuntu version
    try:
        with open('/etc/os-release', 'r') as f:
            os_release = f.read()
            print("OS Release info:")
            for line in os_release.split('\n'):
                if line.startswith(('VERSION=', 'VERSION_ID=', 'UBUNTU_CODENAME=')):
                    print(f"  {line}")
    except Exception as e:
        print(f"Could not read OS release: {e}")
    
    # Check for snap-installed Python (common Ubuntu issue)
    python_path = sys.executable
    print(f"\nPython executable path: {python_path}")
    if 'snap' in python_path:
        print("⚠️  WARNING: Using snap-installed Python, which can cause SSL issues")
    
    print("=" * 80)

def check_ssl_context_creation():
    """Test SSL context creation with different settings"""
    print("SSL CONTEXT TESTING")
    print("=" * 80)
    
    contexts_to_test = [
        ("Default context", lambda: ssl.create_default_context()),
        ("Client context", lambda: ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)),
        ("Server context", lambda: ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)),
    ]
    
    for name, context_func in contexts_to_test:
        try:
            ctx = context_func()
            print(f"✅ {name}: OK")
            print(f"   Protocol: {ctx.protocol}")
            print(f"   Options: {ctx.options}")
            print(f"   Verify mode: {ctx.verify_mode}")
        except Exception as e:
            print(f"❌ {name}: FAILED - {e}")
    
    print("=" * 80)

async def test_basic_webrtc_connection():
    """Test basic WebRTC connection without data channel focus"""
    print("BASIC WEBRTC CONNECTION TEST")
    print("=" * 80)
    
    try:
        conn = Go2WebRTCConnection(WebRTCConnectionMethod.LocalSTA, ip="***********")
        
        # Monitor connection states
        connection_states = {
            'ice_connection': 'new',
            'ice_gathering': 'new', 
            'signaling': 'stable',
            'peer_connection': 'new',
            'dtls_transport': 'unknown'
        }
        
        def print_state_change(state_type, new_state):
            connection_states[state_type] = new_state
            print(f"State change - {state_type}: {new_state}")
        
        # Try to connect with detailed monitoring
        print("Attempting WebRTC connection...")
        await asyncio.wait_for(conn.connect(), timeout=60)
        
        print("✅ WebRTC connection successful!")
        
        # Check final states
        print("\nFinal connection states:")
        for state_type, state in connection_states.items():
            print(f"  {state_type}: {state}")
        
        # Check data channel specifically
        if hasattr(conn, 'datachannel') and conn.datachannel:
            dc = conn.datachannel
            print(f"\nData channel info:")
            print(f"  Ready state: {dc.channel.readyState}")
            print(f"  Opened flag: {dc.data_channel_opened}")
            
            # Try to access DTLS transport info
            try:
                pc = conn.pc
                if hasattr(pc, '_RTCPeerConnection__sctp') and pc._RTCPeerConnection__sctp:
                    sctp = pc._RTCPeerConnection__sctp
                    if hasattr(sctp, 'transport') and sctp.transport:
                        dtls = sctp.transport
                        print(f"  DTLS transport state: {dtls.state}")
                        
                        # Check for Ubuntu-specific DTLS issues
                        if hasattr(dtls, '_RTCDtlsTransport__ssl_context'):
                            ssl_ctx = dtls._RTCDtlsTransport__ssl_context
                            print(f"  SSL context protocol: {ssl_ctx.protocol}")
                            print(f"  SSL context options: {ssl_ctx.options}")
            except Exception as e:
                print(f"  Could not get DTLS info: {e}")
        
        return True
        
    except asyncio.TimeoutError:
        print("❌ Connection timed out")
        return False
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ubuntu_specific_fixes():
    """Test Ubuntu-specific potential fixes"""
    print("UBUNTU-SPECIFIC FIXES TEST")
    print("=" * 80)
    
    fixes_to_test = []
    
    # Fix 1: Force specific SSL/TLS versions
    def fix1():
        original_create = ssl.create_default_context
        def patched_create(*args, **kwargs):
            ctx = original_create(*args, **kwargs)
            # Force TLS 1.2 (more compatible)
            ctx.minimum_version = ssl.TLSVersion.TLSv1_2
            ctx.maximum_version = ssl.TLSVersion.TLSv1_2
            return ctx
        ssl.create_default_context = patched_create
        return lambda: setattr(ssl, 'create_default_context', original_create)
    
    fixes_to_test.append(("Force TLS 1.2", fix1))
    
    # Fix 2: Disable certain SSL options
    def fix2():
        original_create = ssl.create_default_context
        def patched_create(*args, **kwargs):
            ctx = original_create(*args, **kwargs)
            # Disable problematic options
            ctx.options &= ~ssl.OP_NO_SSLv3
            ctx.options |= ssl.OP_NO_COMPRESSION
            return ctx
        ssl.create_default_context = patched_create
        return lambda: setattr(ssl, 'create_default_context', original_create)
    
    fixes_to_test.append(("Adjust SSL options", fix2))
    
    # Fix 3: Set specific cipher suites
    def fix3():
        original_create = ssl.create_default_context
        def patched_create(*args, **kwargs):
            ctx = original_create(*args, **kwargs)
            # Set specific cipher suites that work better with DTLS
            try:
                ctx.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
            except:
                pass  # Fallback to default
            return ctx
        ssl.create_default_context = patched_create
        return lambda: setattr(ssl, 'create_default_context', original_create)
    
    fixes_to_test.append(("Custom cipher suites", fix3))
    
    results = {}
    
    for fix_name, fix_func in fixes_to_test:
        print(f"\n🧪 Testing fix: {fix_name}")
        
        try:
            # Apply fix
            restore_func = fix_func()
            
            # Test connection
            success = await test_basic_webrtc_connection()
            results[fix_name] = success
            
            # Restore original
            restore_func()
            
            print(f"Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            
        except Exception as e:
            print(f"❌ Fix failed with error: {e}")
            results[fix_name] = False
        
        # Wait between tests
        await asyncio.sleep(2)
    
    print(f"\n" + "=" * 80)
    print("UBUNTU FIXES RESULTS")
    print("=" * 80)
    
    for fix_name, success in results.items():
        status = "✅ WORKED" if success else "❌ FAILED"
        print(f"{fix_name}: {status}")
    
    successful_fixes = [name for name, success in results.items() if success]
    if successful_fixes:
        print(f"\n🎉 Working fixes: {', '.join(successful_fixes)}")
    else:
        print(f"\n😞 No fixes worked. This suggests a deeper compatibility issue.")

async def main():
    """Main Ubuntu debugging function"""
    print_detailed_system_info()
    check_ssl_context_creation()
    
    print("\n" + "=" * 80)
    print("STARTING WEBRTC TESTS")
    print("=" * 80)
    
    # Test basic connection first
    basic_success = await test_basic_webrtc_connection()
    
    if not basic_success:
        print("\nBasic connection failed. Testing Ubuntu-specific fixes...")
        await test_ubuntu_specific_fixes()
    else:
        print("\n✅ Basic connection worked! The issue might be data-channel specific.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
        sys.exit(0)
