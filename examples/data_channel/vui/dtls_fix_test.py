#!/usr/bin/env python3
"""
DTLS Fix Test Script

This script tests different approaches to fix the DTLS handshake issue on macOS/Ubuntu.
"""

import asyncio
import logging
import ssl
import os
import sys
from go2_webrtc_driver.webrtc_driver import Go2WebRTCConnection, WebRTCConnectionMethod

# Enable detailed logging
logging.basicConfig(level=logging.DEBUG)

async def test_with_ssl_context_override():
    """Test with custom SSL context settings"""
    print("=" * 60)
    print("TESTING WITH SSL CONTEXT OVERRIDE")
    print("=" * 60)
    
    # Try to override SSL context settings
    original_create_default_context = ssl.create_default_context
    
    def patched_create_default_context(*args, **kwargs):
        ctx = original_create_default_context(*args, **kwargs)
        
        # Try to use older TLS/DTLS settings that might be more compatible
        try:
            # Disable certain OpenSSL 3.0 features that might cause issues
            ctx.options |= ssl.OP_LEGACY_SERVER_CONNECT
        except AttributeError:
            pass
        
        try:
            # Set minimum protocol version
            ctx.minimum_version = ssl.TLSVersion.TLSv1_2
        except AttributeError:
            pass
        
        # Disable certificate verification for testing
        ctx.check_hostname = False
        ctx.verify_mode = ssl.CERT_NONE
        
        print(f"Custom SSL context created with options: {ctx.options}")
        return ctx
    
    # Monkey patch SSL context creation
    ssl.create_default_context = patched_create_default_context
    
    try:
        conn = Go2WebRTCConnection(WebRTCConnectionMethod.LocalSTA, ip="***********")
        await asyncio.wait_for(conn.connect(), timeout=45)
        
        if conn.datachannel and conn.datachannel.data_channel_opened:
            print("✅ SUCCESS: Data channel opened with SSL context override!")
            return True
        else:
            print("❌ FAILED: Data channel still failed to open")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: Exception occurred: {e}")
        return False
    finally:
        # Restore original function
        ssl.create_default_context = original_create_default_context

async def test_with_environment_variables():
    """Test with OpenSSL environment variables"""
    print("=" * 60)
    print("TESTING WITH OPENSSL ENVIRONMENT VARIABLES")
    print("=" * 60)
    
    # Set environment variables that might help with OpenSSL 3.0 compatibility
    env_vars = {
        'OPENSSL_CONF': '',  # Disable OpenSSL config
        'SSL_CERT_FILE': '',  # Disable cert file
        'SSL_CERT_DIR': '',   # Disable cert dir
        'OPENSSL_ia32cap': '~0x200000200000000',  # Disable certain CPU features
    }
    
    # Backup original environment
    original_env = {}
    for key, value in env_vars.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
        print(f"Set {key}={value}")
    
    try:
        conn = Go2WebRTCConnection(WebRTCConnectionMethod.LocalSTA, ip="***********")
        await asyncio.wait_for(conn.connect(), timeout=45)
        
        if conn.datachannel and conn.datachannel.data_channel_opened:
            print("✅ SUCCESS: Data channel opened with environment variables!")
            return True
        else:
            print("❌ FAILED: Data channel still failed to open")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: Exception occurred: {e}")
        return False
    finally:
        # Restore original environment
        for key, original_value in original_env.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value

async def test_with_aiortc_patches():
    """Test with aiortc library patches"""
    print("=" * 60)
    print("TESTING WITH AIORTC PATCHES")
    print("=" * 60)
    
    try:
        # Try to patch aiortc's DTLS transport
        import aiortc.rtcdtlstransport
        
        original_init = aiortc.rtcdtlstransport.RTCDtlsTransport.__init__
        
        def patched_init(self, transport, certificates, role="auto"):
            print(f"Patching DTLS transport initialization with role: {role}")
            
            # Call original init
            result = original_init(self, transport, certificates, role)
            
            # Try to modify SSL context after initialization
            if hasattr(self, '_RTCDtlsTransport__ssl_context'):
                ctx = self._RTCDtlsTransport__ssl_context
                print(f"Modifying SSL context: {ctx}")
                
                # Try to set more permissive options
                try:
                    ctx.options |= ssl.OP_LEGACY_SERVER_CONNECT
                    ctx.check_hostname = False
                    ctx.verify_mode = ssl.CERT_NONE
                    print("Applied SSL context patches")
                except Exception as e:
                    print(f"Could not apply SSL context patches: {e}")
            
            return result
        
        # Apply patch
        aiortc.rtcdtlstransport.RTCDtlsTransport.__init__ = patched_init
        
        conn = Go2WebRTCConnection(WebRTCConnectionMethod.LocalSTA, ip="***********")
        await asyncio.wait_for(conn.connect(), timeout=45)
        
        if conn.datachannel and conn.datachannel.data_channel_opened:
            print("✅ SUCCESS: Data channel opened with aiortc patches!")
            return True
        else:
            print("❌ FAILED: Data channel still failed to open")
            return False
            
    except Exception as e:
        print(f"❌ FAILED: Exception occurred: {e}")
        return False
    finally:
        # Restore original function
        try:
            aiortc.rtcdtlstransport.RTCDtlsTransport.__init__ = original_init
        except:
            pass

async def main():
    """Run all tests"""
    print("DTLS FIX TESTING")
    print("=" * 60)
    print("This script tests different approaches to fix DTLS handshake issues")
    print("on macOS/Ubuntu with OpenSSL 3.0+")
    print("=" * 60)
    
    tests = [
        ("SSL Context Override", test_with_ssl_context_override),
        ("Environment Variables", test_with_environment_variables),
        ("aiortc Patches", test_with_aiortc_patches),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        try:
            success = await test_func()
            results[test_name] = success
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results[test_name] = False
        
        print(f"Result: {'✅ PASSED' if results[test_name] else '❌ FAILED'}")
        
        # Wait a bit between tests
        await asyncio.sleep(2)
    
    print("\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    successful_tests = [name for name, success in results.items() if success]
    if successful_tests:
        print(f"\n🎉 Successful approaches: {', '.join(successful_tests)}")
    else:
        print("\n😞 No approaches were successful")
        print("\nThis suggests the issue may require:")
        print("1. Downgrading OpenSSL to 1.1.x")
        print("2. Using a different WebRTC library")
        print("3. Patching aiortc for OpenSSL 3.0 compatibility")
        print("4. Using the Windows environment where it works")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
        sys.exit(0)
