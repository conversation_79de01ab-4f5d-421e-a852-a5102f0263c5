# DTLS Data Channel Fix for macOS/Ubuntu

## Problem
The WebRTC data channel fails to open on macOS/Ubuntu due to DTLS handshake failures with OpenSSL 3.0+. This is a known compatibility issue where the DTLS transport gets stuck in "connecting" state and eventually fails.

## Root Cause
- **OpenSSL 3.0+ Compatibility**: OpenSSL 3.0 introduced breaking changes to DTLS handling
- **Platform-Specific**: Windows uses different SSL implementations that don't have this issue
- **aiortc Library**: The aiortc library hasn't been fully updated for OpenSSL 3.0 compatibility

## Solutions

### Solution 1: Use Docker with OpenSSL 1.1.x (Recommended)

Create a Docker container with OpenSSL 1.1.x:

```dockerfile
# Dockerfile
FROM python:3.11-slim-bullseye

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libssl1.1 \
    libssl-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application code
COPY . .

CMD ["python", "vui.py"]
```

### Solution 2: Use Conda with OpenSSL 1.1.x

```bash
# Create conda environment with OpenSSL 1.1.x
conda create -n go2_webrtc python=3.11 openssl=1.1.1
conda activate go2_webrtc

# Install dependencies
pip install -r requirements.txt

# Run the application
python vui.py
```

### Solution 3: Use pyenv with Python compiled against OpenSSL 1.1.x

```bash
# Install OpenSSL 1.1.x via Homebrew (macOS)
brew install openssl@1.1

# Set environment variables
export LDFLAGS="-L$(brew --prefix openssl@1.1)/lib"
export CPPFLAGS="-I$(brew --prefix openssl@1.1)/include"
export PKG_CONFIG_PATH="$(brew --prefix openssl@1.1)/lib/pkgconfig"

# Install Python with OpenSSL 1.1.x
pyenv install 3.11.9
pyenv local 3.11.9

# Install dependencies
pip install -r requirements.txt
```

### Solution 4: Patch aiortc for OpenSSL 3.0 (Advanced)

This requires modifying the aiortc library source code to handle OpenSSL 3.0 properly. This is complex and not recommended for production use.

## Verification

To verify which OpenSSL version you're using:

```python
import ssl
print(f"OpenSSL version: {ssl.OPENSSL_VERSION}")
print(f"Version info: {ssl.OPENSSL_VERSION_INFO}")
```

**Working versions:**
- OpenSSL 1.1.1x (any patch version)
- OpenSSL 1.0.2x (older, but works)

**Problematic versions:**
- OpenSSL 3.0.x (current issue)
- OpenSSL 3.1.x (also problematic)

## Current System Information

Your current system:
- Platform: Darwin 24.5.0 (macOS)
- Python: 3.12.9
- OpenSSL: 3.0.15 (problematic version)
- PyOpenSSL: 25.1.0

## Recommended Next Steps

1. **Try Docker Solution**: Use the Docker approach for the most reliable fix
2. **Use Conda**: Create a conda environment with OpenSSL 1.1.x
3. **Contact Unitree**: Report this as a compatibility issue for future firmware updates
4. **Use Windows**: As a temporary workaround, use the Windows environment where it works

## Alternative Workarounds

If you cannot change the OpenSSL version:

1. **Use Windows Subsystem for Linux (WSL)** with an older Ubuntu version
2. **Use a Virtual Machine** with Ubuntu 20.04 (uses OpenSSL 1.1.1)
3. **Use GitHub Codespaces** or similar cloud development environments

## Testing

After implementing any solution, test with:

```bash
python dtls_debug.py
```

Look for:
- ✅ DTLS Transport state: "connected" 
- ✅ Data Channel state: "open"
- ✅ No "DTLS handshake failed" errors
