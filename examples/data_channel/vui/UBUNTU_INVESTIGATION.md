# Ubuntu DTLS Investigation Guide

## Background
You mentioned that Ubuntu has OpenSSL 1.1.1f, which should be compatible with WebRTC DTLS. This suggests the issue on Ubuntu might be different from the macOS OpenSSL 3.0 problem.

## Potential Ubuntu-Specific Issues

### 1. **Python Installation Method**
- **Snap-installed Python**: Ubuntu's snap-packaged Python can have SSL library conflicts
- **System Python vs. User Python**: Different Python installations may link to different OpenSSL versions
- **Virtual environments**: May inherit SSL issues from the base Python

### 2. **Library Conflicts**
- **Multiple OpenSSL versions**: System might have both 1.1.1f and 3.0+ installed
- **Python compiled against different OpenSSL**: Python might be compiled against a different OpenSSL than what's reported
- **PyOpenSSL vs. built-in ssl**: Conflicts between PyOpenSSL and Python's built-in ssl module

### 3. **Ubuntu-Specific SSL Configuration**
- **System SSL policies**: Ubuntu may have restrictive SSL/TLS policies
- **Certificate store issues**: Different certificate validation behavior
- **Firewall/network restrictions**: Ubuntu firewall might interfere with DTLS

## Investigation Steps

### Step 1: Run the Ubuntu Debug Script

Copy the `ubuntu_debug.py` script to your Ubuntu system and run:

```bash
python3 ubuntu_debug.py
```

This will provide detailed information about:
- Exact OpenSSL version used by Python
- Python installation method (snap, apt, compiled)
- SSL library conflicts
- Ubuntu version and configuration

### Step 2: Check for Common Ubuntu Issues

```bash
# Check Python installation method
which python3
python3 -c "import sys; print(sys.executable)"

# Check for snap Python (problematic)
snap list | grep python

# Check OpenSSL libraries
dpkg -l | grep ssl
ldconfig -p | grep ssl

# Check Ubuntu version
cat /etc/os-release
```

### Step 3: Test Different Python Environments

```bash
# Test with system Python
/usr/bin/python3 -c "import ssl; print(ssl.OPENSSL_VERSION)"

# Test with different Python if available
python3.8 -c "import ssl; print(ssl.OPENSSL_VERSION)" 2>/dev/null || echo "Python 3.8 not available"
python3.9 -c "import ssl; print(ssl.OPENSSL_VERSION)" 2>/dev/null || echo "Python 3.9 not available"
python3.10 -c "import ssl; print(ssl.OPENSSL_VERSION)" 2>/dev/null || echo "Python 3.10 not available"
```

## Potential Solutions for Ubuntu

### Solution 1: Use System Python (not snap)

If you're using snap Python:

```bash
# Remove snap Python
sudo snap remove python3

# Install system Python
sudo apt update
sudo apt install python3 python3-pip python3-venv

# Create virtual environment
python3 -m venv go2_env
source go2_env/bin/activate
pip install -r requirements.txt
```

### Solution 2: Force OpenSSL 1.1.1 Linking

```bash
# Install OpenSSL 1.1.1 development packages
sudo apt install libssl1.1 libssl-dev

# Reinstall Python packages that depend on SSL
pip uninstall aiortc
pip install --no-cache-dir aiortc
```

### Solution 3: Use pyenv with Custom Python Build

```bash
# Install pyenv
curl https://pyenv.run | bash

# Install dependencies
sudo apt install -y make build-essential libssl-dev zlib1g-dev \
    libbz2-dev libreadline-dev libsqlite3-dev wget curl llvm \
    libncurses5-dev libncursesw5-dev xz-utils tk-dev libffi-dev \
    liblzma-dev python3-openssl git

# Install Python with specific OpenSSL
CONFIGURE_OPTS="--with-openssl=/usr" pyenv install 3.11.9
pyenv local 3.11.9
pip install -r requirements.txt
```

### Solution 4: Use Docker (Most Reliable)

```bash
# Create Dockerfile for Ubuntu with working SSL
cat > Dockerfile << 'EOF'
FROM ubuntu:20.04

# Install dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    libssl1.1 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip3 install -r requirements.txt

COPY . .
CMD ["python3", "vui.py"]
EOF

# Build and run
docker build -t go2-webrtc .
docker run --network host go2-webrtc
```

## Expected Debug Output

When you run `ubuntu_debug.py`, look for:

### ✅ Good Signs:
- `Python SSL version: OpenSSL 1.1.1f`
- `System OpenSSL: OpenSSL 1.1.1f`
- Python executable NOT in `/snap/`
- SSL context creation succeeds
- No library conflicts

### ⚠️ Warning Signs:
- Python executable in `/snap/bin/`
- Multiple OpenSSL versions listed
- SSL context creation fails
- `PyOpenSSL` version conflicts

### ❌ Problem Indicators:
- `OpenSSL 3.0.x` reported by Python (despite system having 1.1.1f)
- SSL library loading errors
- Certificate verification failures

## Next Steps

1. **Run the debug script** and share the output
2. **Try the solutions** in order of preference
3. **Compare with Windows** - check what OpenSSL version Windows is using
4. **Test with minimal setup** - try a fresh Ubuntu VM or container

## Questions to Answer

1. What Ubuntu version are you using? (`lsb_release -a`)
2. How was Python installed? (apt, snap, compiled, pyenv, etc.)
3. Are you using a virtual environment?
4. What's the output of `python3 -c "import ssl; print(ssl.OPENSSL_VERSION)"`?
5. Does the same code work in a Docker container with Ubuntu 20.04?

The fact that it works on Windows but not on Ubuntu with OpenSSL 1.1.1f suggests this is likely a Python/library configuration issue rather than a fundamental OpenSSL compatibility problem.
